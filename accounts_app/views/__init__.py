# --- Import all views for compatibility ---

# Import email utilities for test patching compatibility
from utility_app.email_utils import send_welcome_email

# Common utilities and business landing page
from .common import (
    get_client_ip, record_login_attempt, business_landing_view, MESSAGES
)

# Customer views
from .customer import (
    CustomerSignupView, customer_login_view, unified_logout_view,
    CustomerProfileView, CustomerProfileEditView,
    customer_change_password_view, customer_deactivate_account_view,
    CustomerPasswordResetView, CustomerPasswordResetDoneView,
    CustomerPasswordResetConfirmView, CustomerPasswordResetCompleteView,
)

# Provider views
from .provider import (
    ServiceProviderSignupView, provider_signup_done_view, provider_email_verify_view,
    service_provider_login_view,
    ServiceProviderProfileView, ServiceProviderProfileEditView,
    service_provider_change_password_view, service_provider_deactivate_account_view,
    ServiceProviderPasswordResetView, ServiceProviderPasswordResetDoneView,
    ServiceProviderPasswordResetConfirmView, ServiceProviderPasswordResetCompleteView,
    premium_upgrade,
)

# Team management views
from .team import (
    team_member_list_view, team_member_add_view, team_member_edit_view,
    team_member_delete_view, team_member_toggle_status_view,
)

# Allauth integration views
from .allauth_integration import (
    CustomerSignupRedirectView, ServiceProviderSignupRedirectView,
    unified_login_redirect_view, CustomerLoginRedirectView, ServiceProviderLoginRedirectView,
    post_auth_redirect_view, role_switch_redirect_view,
    enhanced_unified_login_view, unified_logout_integration_view, UnifiedAuthenticationMixin,
)

# Profile preview services
from ..services.profile_preview import (
    profile_preview_ajax,
    profile_completion_preview_ajax,
)

# Profile completion workflow services
from ..services.profile_completion_workflows import (
    profile_completion_workflow_ajax,
    trigger_completion_check_ajax,
    start_guided_tour_ajax,
)

# Privacy management services
from ..services.privacy_management import (
    privacy_dashboard_ajax,
    apply_privacy_preset_ajax,
    bulk_update_privacy_ajax,
    privacy_impact_analysis_ajax,
)

# Make all views available at package level for backward compatibility
__all__ = [
    # Email utilities
    'send_welcome_email',
    # Common
    'get_client_ip', 'record_login_attempt', 'business_landing_view', 'MESSAGES',
    # Customer views
    'CustomerSignupView', 'customer_login_view', 'unified_logout_view',
    'CustomerProfileView', 'CustomerProfileEditView',
    'customer_change_password_view', 'customer_deactivate_account_view',
    'CustomerPasswordResetView', 'CustomerPasswordResetDoneView',
    'CustomerPasswordResetConfirmView', 'CustomerPasswordResetCompleteView',
    # Provider views
    'ServiceProviderSignupView', 'provider_signup_done_view', 'provider_email_verify_view',
    'service_provider_login_view',
    'ServiceProviderProfileView', 'ServiceProviderProfileEditView',
    'service_provider_change_password_view', 'service_provider_deactivate_account_view',
    'ServiceProviderPasswordResetView', 'ServiceProviderPasswordResetDoneView',
    'ServiceProviderPasswordResetConfirmView', 'ServiceProviderPasswordResetCompleteView',
    # Team management views
    'team_member_list_view', 'team_member_add_view', 'team_member_edit_view',
    'team_member_delete_view', 'team_member_toggle_status_view',
    # Premium features
    'premium_upgrade',
    # Allauth integration views
    'CustomerSignupRedirectView', 'ServiceProviderSignupRedirectView',
    'unified_login_redirect_view', 'CustomerLoginRedirectView', 'ServiceProviderLoginRedirectView',
    'post_auth_redirect_view', 'role_switch_redirect_view',
    'enhanced_unified_login_view', 'unified_logout_integration_view', 'UnifiedAuthenticationMixin',
    # Profile preview services
    'profile_preview_ajax', 'profile_completion_preview_ajax',
    # Profile completion workflow services
    'profile_completion_workflow_ajax', 'trigger_completion_check_ajax', 'start_guided_tour_ajax',
]
