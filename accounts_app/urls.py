from django.urls import path
from django.views.generic import TemplateView
from . import views
from .views.email_verification import (
    EmailVerificationStatusView,
    ResendVerificationEmailView,
    email_verification_check_ajax,
    EmailVerificationSuccessView,
    EmailVerificationErrorView,
)
from .views.account_recovery import (
    AccountRecoveryView,
    AccountRecoverySubmittedView,
    AccountUnlockRequestView,
    AccountUnlockSubmittedView,
)
from .views.session_management import (
    SessionManagementView,
    TerminateSessionView,
    extend_session_view,
    session_status_ajax,
    SessionSecurityView,
)
from .views.social_account_management import (
    SocialAccountManagementView,
    UnlinkSocialAccountView,
    SyncSocialAccountDataView,
    SocialAccountConflictResolutionView,
)


app_name = 'accounts_app'

urlpatterns = [
    # Business Information Page
    path('for-business/', views.business_landing_view, name='for_business'),

    # Role-based Allauth Integration URLs (New - preferred approach)
    path('customer/signup/allauth/', views.CustomerSignupRedirectView.as_view(), name='customer_signup_allauth'),
    path('provider/signup/allauth/', views.ServiceProviderSignupRedirectView.as_view(), name='provider_signup_allauth'),
    path('customer/login/allauth/', views.CustomerLoginRedirectView.as_view(), name='customer_login_allauth'),
    path('provider/login/allauth/', views.ServiceProviderLoginRedirectView.as_view(), name='provider_login_allauth'),
    path('login/unified/', views.unified_login_redirect_view, name='unified_login_redirect'),

    # Smart redirect URLs for post-authentication routing
    path('redirect/post-auth/', views.post_auth_redirect_view, name='post_auth_redirect'),
    path('redirect/role-switch/', views.role_switch_redirect_view, name='role_switch_redirect'),

    # Enhanced unified authentication URLs
    path('login/enhanced/', views.enhanced_unified_login_view, name='enhanced_unified_login'),
    path('logout/unified/', views.unified_logout_integration_view, name='unified_logout_integration'),

    # Enhanced Email Verification URLs
    path('email/verification/status/', EmailVerificationStatusView.as_view(), name='email_verification_status'),
    path('email/verification/resend/', ResendVerificationEmailView.as_view(), name='resend_verification_email'),
    path('email/verification/check/', email_verification_check_ajax, name='email_verification_check_ajax'),
    path('email/verification/success/', EmailVerificationSuccessView.as_view(), name='email_verification_success'),
    path('email/verification/error/', EmailVerificationErrorView.as_view(), name='email_verification_error'),

    # Enhanced Account Recovery URLs
    path('recovery/', AccountRecoveryView.as_view(), name='account_recovery'),
    path('recovery/submitted/', AccountRecoverySubmittedView.as_view(), name='account_recovery_submitted'),
    path('unlock/request/', AccountUnlockRequestView.as_view(), name='account_unlock_request'),
    path('unlock/submitted/', AccountUnlockSubmittedView.as_view(), name='account_unlock_submitted'),

    # Enhanced Session Management URLs
    path('sessions/', SessionManagementView.as_view(), name='session_management'),
    path('sessions/terminate/', TerminateSessionView.as_view(), name='terminate_session'),
    path('sessions/extend/', extend_session_view, name='extend_session'),
    path('sessions/status/', session_status_ajax, name='session_status_ajax'),
    path('sessions/security/', SessionSecurityView.as_view(), name='session_security'),

    # Social Account Management URLs
    path('social/', SocialAccountManagementView.as_view(), name='social_account_management'),
    path('social/unlink/', UnlinkSocialAccountView.as_view(), name='unlink_social_account'),
    path('social/sync/', SyncSocialAccountDataView.as_view(), name='sync_social_account_data'),
    path('social/conflict/<int:conflict_id>/', SocialAccountConflictResolutionView.as_view(), name='social_account_conflict_resolution'),

    # Security URLs
    path('rate-limit-exceeded/', TemplateView.as_view(template_name='accounts/rate_limit_exceeded.html'), name='rate_limit_exceeded'),

    # Customer Authentication URLs (Legacy - maintained for backward compatibility)
    path('customer/signup/', views.CustomerSignupView.as_view(), name='customer_signup'),
    path('customer/login/', views.customer_login_view, name='customer_login'),
    path('logout/', views.unified_logout_view, name='logout'),
    
    # Customer Profile URLs
    path('customer/profile/', views.CustomerProfileView.as_view(), name='customer_profile'),
    path('customer/profile/edit/', views.CustomerProfileEditView.as_view(), name='customer_profile_edit'),
    path('customer/change-password/', views.customer_change_password_view, name='customer_change_password'),
    path('customer/deactivate/', views.customer_deactivate_account_view, name='customer_deactivate'),

    # Profile Preview AJAX URLs
    path('profile/preview/', views.profile_preview_ajax, name='profile_preview_ajax'),
    path('profile/completion-preview/', views.profile_completion_preview_ajax, name='profile_completion_preview_ajax'),

    # Profile Completion Workflow URLs
    path('profile/completion-workflow/', views.profile_completion_workflow_ajax, name='profile_completion_workflow_ajax'),
    path('profile/trigger-completion-check/', views.trigger_completion_check_ajax, name='trigger_completion_check_ajax'),
    path('profile/start-guided-tour/', views.start_guided_tour_ajax, name='start_guided_tour_ajax'),

    # Privacy Management URLs
    path('privacy/dashboard/', views.privacy_dashboard_ajax, name='privacy_dashboard_ajax'),
    path('privacy/apply-preset/', views.apply_privacy_preset_ajax, name='apply_privacy_preset_ajax'),
    path('privacy/bulk-update/', views.bulk_update_privacy_ajax, name='bulk_update_privacy_ajax'),
    path('privacy/impact-analysis/', views.privacy_impact_analysis_ajax, name='privacy_impact_analysis_ajax'),
    
    # Customer Password Reset URLs
    path('customer/password-reset/', views.CustomerPasswordResetView.as_view(), name='customer_password_reset'),
    path('customer/password-reset/done/', views.CustomerPasswordResetDoneView.as_view(), name='customer_password_reset_done'),
    path('customer/password-reset/confirm/<uidb64>/<token>/', views.CustomerPasswordResetConfirmView.as_view(), name='customer_password_reset_confirm'),
    path('customer/password-reset/complete/', views.CustomerPasswordResetCompleteView.as_view(), name='customer_password_reset_complete'),

    # Service Provider Authentication URLs (Legacy - maintained for backward compatibility)
    path('provider/signup/', views.ServiceProviderSignupView.as_view(), name='service_provider_signup'),
    path('provider/signup/done/', views.provider_signup_done_view, name='provider_signup_done'),
    path('provider/verify/<uidb64>/<token>/', views.provider_email_verify_view, name='provider_email_verify'),
    path('provider/login/', views.service_provider_login_view, name='service_provider_login'),

    # Service Provider Profile URLs
    path('provider/profile/', views.ServiceProviderProfileView.as_view(), name='service_provider_profile'),
    path('provider/profile/edit/', views.ServiceProviderProfileEditView.as_view(), name='service_provider_profile_edit'),
    path('provider/change-password/', views.service_provider_change_password_view, name='service_provider_change_password'),
    path('provider/deactivate/', views.service_provider_deactivate_account_view, name='service_provider_deactivate'),

    # Team Management URLs
    path('provider/team/', views.team_member_list_view, name='team_member_list'),
    path('provider/team/add/', views.team_member_add_view, name='team_member_add'),
    path('provider/team/<int:member_id>/edit/', views.team_member_edit_view, name='team_member_edit'),
    path('provider/team/<int:member_id>/delete/', views.team_member_delete_view, name='team_member_delete'),
    path('provider/team/<int:member_id>/toggle/', views.team_member_toggle_status_view, name='team_member_toggle_status'),

    # Service Provider Password Reset URLs
    path('provider/password-reset/', views.ServiceProviderPasswordResetView.as_view(), name='service_provider_password_reset'),
    path('provider/password-reset/done/', views.ServiceProviderPasswordResetDoneView.as_view(), name='service_provider_password_reset_done'),
    path('provider/password-reset/confirm/<uidb64>/<token>/', views.ServiceProviderPasswordResetConfirmView.as_view(), name='service_provider_password_reset_confirm'),
    path('provider/password-reset/complete/', views.ServiceProviderPasswordResetCompleteView.as_view(), name='service_provider_password_reset_complete'),

    # Premium Features
    path('premium/upgrade/', views.premium_upgrade, name='premium_upgrade'),
]
